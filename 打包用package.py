import subprocess
import sys
import os

def package_project():
    try:
        print("开始打包...")

        # 方法1：使用spec文件打包（推荐）
        if os.path.exists('main.spec'):
            print("使用main.spec文件打包...")
            command = ['pyinstaller', 'main.spec']
        else:
            # 方法2：直接命令行打包
            print("使用命令行参数打包...")
            command = [
                'pyinstaller',
                '--onefile',
                '--windowed',  # 重要：确保不显示控制台窗口
                '--noconsole',  # 额外确保不显示控制台
                '--name=视频混剪工具',
                '--icon=icon.ico' if os.path.exists('icon.ico') else '',
                '--hidden-import=utils',
                '--hidden-import=video_mixer',
                '--hidden-import=transition_mixer',
                '--hidden-import=frame_removal',
                '--hidden-import=machine_code_verifier',
                '--hidden-import=machine_code_detector',
                '--hidden-import=time_validator',
                '--hidden-import=machine_code_converter',
                '--hidden-import=startup_optimizer',
                '--hidden-import=authorization_config_generator',
                '--hidden-import=PySide6.QtCore',
                '--hidden-import=PySide6.QtWidgets',
                '--hidden-import=PySide6.QtGui',
                '--add-data=ui_main_window.py;.' if os.name == 'nt' else '--add-data=ui_main_window.py:.',
                '--distpath=dist',
                '--workpath=build',
                '--specpath=.',
                'main.py'
            ]
            # 移除空的icon参数
            command = [arg for arg in command if arg]

        result = subprocess.run(command, check=True, capture_output=True, text=True)
        print("项目打包成功！")
        print("可执行文件位于: dist/视频混剪工具.exe")
        print("\n重要提示：")
        print("1. 打包后的exe文件不会显示控制台窗口")
        print("2. 所有日志信息都会显示在程序界面中")
        print("3. 确保FFmpeg路径正确配置")

    except subprocess.CalledProcessError as e:
        print(f"打包失败: {e}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
    except Exception as e:
        print(f"发生错误: {e}")

if __name__ == "__main__":
    package_project()