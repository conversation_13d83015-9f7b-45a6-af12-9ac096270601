# -*- mode: python ; coding: utf-8 -*-
import os

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('ui_main_window.py', '.'),
    ],
    hiddenimports=[
        'utils',
        'video_mixer',
        'transition_mixer',
        'frame_removal',
        'machine_code_verifier',
        'machine_code_detector',
        'time_validator',
        'machine_code_converter',
        'startup_optimizer',
        'authorization_config_generator',
        'PySide6.QtCore',
        'PySide6.QtWidgets',
        'PySide6.QtGui',
        'subprocess',
        'json',
        'pathlib',
        'random',
        're',
        'hashlib',
        'uuid',
        'platform',
        'time',
        'datetime',
        'hmac',
        'base64',
        'cryptography.fernet',
        'requests',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='视频混剪工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 重要：不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
)
