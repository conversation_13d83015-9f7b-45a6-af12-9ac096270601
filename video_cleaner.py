"""
视频洗素材处理器
基于修复v1.4.py的功能，实现视频分辨率、比特率和帧率的统一处理
"""

import os
import subprocess
import threading
from pathlib import Path
from PySide6.QtCore import QObject, Signal
from utils import sanitize_filename

# Windows下隐藏控制台窗口
try:
    from subprocess import CREATE_NO_WINDOW
except ImportError:
    CREATE_NO_WINDOW = 0


class VideoCleanerProcessor(QObject):
    """视频洗素材处理器"""
    
    log_signal = Signal(str)
    progress_signal = Signal(int)
    finished_signal = Signal()
    
    def __init__(self):
        super().__init__()
        self.target_width = 1080
        self.target_height = 1920
        self.target_bitrate = 12000
        self.target_framerate = 30
        self.output_directory = ""
        self.replace_original = False
        self.is_running = False
        
        # FFmpeg路径
        self.ffmpeg_path = self._get_ffmpeg_path()
        
    def _get_ffmpeg_path(self):
        """获取FFmpeg路径"""
        # 尝试多个可能的FFmpeg路径
        possible_paths = [
            "D:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe",
            "ffmpeg.exe",  # 系统PATH中的ffmpeg
            "ffmpeg"       # Linux/Mac系统
        ]
        
        for path in possible_paths:
            if self._check_ffmpeg(path):
                return path
                
        # 如果都找不到，返回默认路径
        return possible_paths[0]
    
    def _check_ffmpeg(self, path):
        """检查FFmpeg是否可用"""
        try:
            result = subprocess.run(
                [path, "-version"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                timeout=10,
                creationflags=CREATE_NO_WINDOW
            )
            return result.returncode == 0
        except:
            return False
    
    def set_target_resolution(self, width, height):
        """设置目标分辨率"""
        self.target_width = width
        self.target_height = height
    
    def set_target_bitrate(self, bitrate):
        """设置目标比特率"""
        self.target_bitrate = bitrate
    
    def set_target_framerate(self, framerate):
        """设置目标帧率"""
        self.target_framerate = framerate
    
    def set_output_directory(self, directory):
        """设置输出目录"""
        self.output_directory = directory
    
    def process_files(self, files, replace_original=False):
        """处理文件列表"""
        self.replace_original = replace_original
        self.is_running = True
        
        # 在新线程中处理
        thread = threading.Thread(target=self._process_files_thread, args=(files,))
        thread.daemon = True
        thread.start()
    
    def _process_files_thread(self, files):
        """在线程中处理文件"""
        try:
            total_files = len(files)
            processed_count = 0
            
            self.log_signal.emit(f"开始处理 {total_files} 个文件...")
            
            for i, file_path in enumerate(files):
                if not self.is_running:
                    self.log_signal.emit("处理已停止")
                    break
                
                self.log_signal.emit(f"正在处理 ({i+1}/{total_files}): {Path(file_path).name}")
                
                success = self._process_single_file(file_path)
                if success:
                    processed_count += 1
                    self.log_signal.emit(f"✅ 处理完成: {Path(file_path).name}")
                else:
                    self.log_signal.emit(f"❌ 处理失败: {Path(file_path).name}")
                
                # 更新进度
                progress = int((i + 1) / total_files * 100)
                self.progress_signal.emit(progress)
            
            self.log_signal.emit(f"处理完成！成功处理 {processed_count}/{total_files} 个文件")
            
        except Exception as e:
            self.log_signal.emit(f"处理过程中发生错误：{str(e)}")
        
        finally:
            self.is_running = False
            self.finished_signal.emit()
    
    def _process_single_file(self, input_path):
        """处理单个文件"""
        try:
            # 确定输出路径
            if self.replace_original:
                # 替换原素材模式：先生成临时文件，然后替换原文件
                input_dir = os.path.dirname(input_path)
                input_name = os.path.basename(input_path)
                name, ext = os.path.splitext(input_name)
                temp_output = os.path.join(input_dir, f"{name}_temp{ext}")
                final_output = input_path
            else:
                # 指定输出目录模式
                input_name = os.path.basename(input_path)
                name, ext = os.path.splitext(input_name)
                safe_name = sanitize_filename(name)
                output_name = f"{safe_name}_cleaned{ext}"
                temp_output = os.path.join(self.output_directory, output_name)
                final_output = temp_output
            
            # 构建FFmpeg命令
            cmd = self._build_ffmpeg_command(input_path, temp_output)
            
            # 执行FFmpeg命令
            self.log_signal.emit(f"执行命令: {cmd}")
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                shell=True,
                creationflags=CREATE_NO_WINDOW
            )
            
            # 等待处理完成
            stdout, _ = process.communicate()
            
            if process.returncode != 0:
                self.log_signal.emit(f"FFmpeg处理失败，返回码：{process.returncode}")
                return False
            
            # 如果是替换原素材模式，需要替换原文件
            if self.replace_original:
                try:
                    # 删除原文件
                    os.remove(input_path)
                    # 重命名临时文件为原文件名
                    os.rename(temp_output, final_output)
                except Exception as e:
                    self.log_signal.emit(f"替换原文件失败：{str(e)}")
                    return False
            
            # 验证输出文件
            if not os.path.exists(final_output) or os.path.getsize(final_output) == 0:
                self.log_signal.emit("输出文件不存在或为空")
                return False
            
            return True
            
        except Exception as e:
            self.log_signal.emit(f"处理文件时发生错误：{str(e)}")
            return False
    
    def _build_ffmpeg_command(self, input_path, output_path):
        """构建FFmpeg命令"""
        # 基础命令：输入文件
        cmd = f'"{self.ffmpeg_path}" -i "{input_path}"'
        
        # 视频处理：分辨率、帧率、比特率
        video_filters = f"scale={self.target_width}:{self.target_height}:force_original_aspect_ratio=disable"
        cmd += f' -vf "{video_filters}"'
        
        # 视频编码参数
        cmd += f' -c:v libx264'
        cmd += f' -b:v {self.target_bitrate}k'
        cmd += f' -minrate {int(self.target_bitrate * 0.9)}k'
        cmd += f' -maxrate {int(self.target_bitrate * 1.1)}k'
        cmd += f' -bufsize {self.target_bitrate * 2}k'
        cmd += f' -r {self.target_framerate}'  # 设置帧率
        cmd += f' -preset medium'
        cmd += f' -pix_fmt yuv420p'
        
        # 音频处理：保持原音频或重新编码
        cmd += f' -c:a aac -b:a 128k'
        
        # 输出文件
        cmd += f' -y "{output_path}"'
        
        return cmd
    
    def stop(self):
        """停止处理"""
        self.is_running = False
